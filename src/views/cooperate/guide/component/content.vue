<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card style="margin-bottom:20px">
        <div slot="header" class="clearfix">
          <span>景区图文资料</span>
          <div style="float:right">
            <el-button size='mini' type="primary" @click="submitForm">保存</el-button>
            <el-button size='mini' @click="cancel">取 消</el-button>
          </div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="讲解词文档" prop="fileList">
              <el-upload
              :file-list="form.fileList"
              class="upload-demo"
              drag
              :action="uploadUrl"
              :headers="headers"
              accept='.doc,.docx,.xml,.xlsx,.xls,.pdf'
              :on-success="handleUploadSuccess"
              :on-remove='handleUploadRemove'
              multiple>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">只能上传doc/docx/xml/xlsx/xls/pdf文件</div>
            </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <el-card style="margin-bottom:20px">
      <div slot="header" class="clearfix">
        <span>景区地理信息</span>
      </div>
      <el-tabs v-model="active" @tab-click="handleClick">
        <el-tab-pane label="景点列表" name="1">
          <el-table
            v-if='!isEdit'
            :data="lableList"
            style="width: 100%">
            <el-table-column label="景点名称" prop="labelName" :show-overflow-tooltip="true" width="200" />
            <el-table-column label="触发半径" prop="radius" width="130" />
            <el-table-column label="是否重复讲解" width="130">
              <template slot-scope="scope">
                <span>{{scope.row.repetition ? '是' : '否'}}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否显示" width="130">
              <template slot-scope="scope">
                <span>{{scope.row.showStatus ? '是' : '否'}}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否聚合" width="130">
              <template slot-scope="scope">
                <span>{{scope.row.joinCluster ? '是' : '否'}}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否讲解" width="130">
              <template slot-scope="scope">
                <span>{{scope.row.explainStatus ? '是' : '否'}}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="审核状态" width="130">
              <template slot-scope="scope">
                <span>{{scope.row.status == 1 ? '审核通过' : '未审核'}}</span>
              </template>
            </el-table-column> -->
            <el-table-column label="操作">
              <template slot-scope="scope">
                <!-- <el-button type="text" @click="examineLabel(scope.row)" v-if='scope.row.status == 0'>审核</el-button> -->
                <el-button type="text" @click="toggleEdit(scope.row)">编辑</el-button>
                <el-button type="text"  @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="景区地图" name="2">
          <div class="store_map">
            <!-- 景区边界检查提示 -->
            <div v-if="!hasScenicBoundary()" class="boundary-warning" style="margin-bottom: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;">
              <i class="el-icon-warning" style="margin-right: 8px;"></i>
              <strong>提示：</strong>当前景区未设置边界，请先前往景区管理页面绘制景区边界，然后才能在地图上标点。
              <el-button type="primary" size="small" style="margin-left: 15px;" @click="goToScenicManagement">
                前往景区管理
              </el-button>
            </div>
            
            <div class="map-controls" style="margin-bottom: 10px;">
              <el-radio-group v-model="mapType" @change="changeMapType" size="small">
                <el-radio-button label="vector">矢量图</el-radio-button>
                <el-radio-button label="satellite">卫星图</el-radio-button>
              </el-radio-group>
            </div>
            <div id="map" style="width:800px;height:450px;margin-left:6%"></div>
            <div style='display: grid'>
              <el-button type="primary" :style="`margin-left:20px;margin-bottom:${(!isDraw && isAdd) ? 0 : 20}px`" @click="resetEditor" v-if='form1.polygon'>重新配置区域</el-button>
              <el-button type="primary" :style="`margin-left:20px;margin-bottom:${isAdd ? 0 : 20}px`" @click="changeDraw(1)" v-if='isAdd && form1.latitude && form1.longitude && !form1.polygon && !isDraw'>画轮廓</el-button>
              <el-button type="primary" style="margin-left:20px" @click="startUpdatePosition" v-if='isEdit && !isAdd && !isDraw && !isUpdatingPosition'>更新位置</el-button>
              <el-button type="primary" style="margin-left:20px" @click="confirmUpdatePosition" v-if='isUpdatingPosition' :style="`margin-bottom: 20px`">确定更新</el-button>
              <el-button type="warning" style="margin-left:20px" @click="cancelUpdatePosition" v-if='isUpdatingPosition'>取消更新</el-button>
              <el-button type="primary" style="margin-left:20px;" @click="addLabel" v-if='!isEdit' :disabled="!hasScenicBoundary()">创建景点</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div v-if='isEdit'>
        <el-form ref="form1" :model="form1" :rules="rules1" label-width="120px" >
          <el-row>
            <el-col :span="10">
              <el-form-item label="景点名称" prop="labelName">
                <el-input v-model="form1.labelName" :disabled="allDisabled" placeholder="请输入景区名称"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="10">
              <el-form-item label="是否讲解" prop="explainStatus">
                <el-select
                  v-model="form1.explainStatus"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="是否聚合" prop="joinCluster">
                <el-select
                  v-model="form1.joinCluster"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="10">
              <el-form-item label="是否显示" prop="showStatus">
                <el-select
                  v-model="form1.showStatus"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="是否重复讲解" prop="repetition">
                <el-select
                  v-model="form1.repetition"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="10">
              <el-form-item label="触发半径" prop="radius">
                <el-input v-model="form1.radius" placeholder="请输入触发半径" :disabled="allDisabled" @input="value => validateInput(value,'radius')">
                  <template slot="append">米</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="复讲间隔时间" prop="intervalTime">
                <el-input v-model="form1.intervalTime" placeholder="请输入复讲间隔时间" :disabled="allDisabled" @input="value => validateInput(value,'intervalTime')">
                  <template slot="append">s</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="10">
              <el-form-item label="腾讯POI分类" prop="categoryCode">
                <el-select
                  v-model="form1.categoryCode"
                  placeholder="腾讯POI分类"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option
                    v-for="dict in dict.type.tmap_category"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="自有POI分类" prop="customizeCategoryCode">
                <el-select
                  v-model="form1.customizeCategoryCode"
                  placeholder="自有POI分类"
                  clearable
                  style="width: 240px"
                  :disabled="allDisabled"
                >
                  <el-option
                    v-for="dict in dict.type.customize_category_code"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align:center">
          <el-button type="primary" @click="submitForm1">确 定</el-button>
          <el-button @click="cancel1">取 消</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { saveOrUpdate, delOrUpdate, getScenicDoc, getLabels, editLabel, delLabel, createLabel, checkLabel, getScenicSpot } from "@/api/cooperate/guide";
import { getToken } from "@/utils/auth";
let map,marker,polygon,editor

export default {
  dicts: ['customize_category_code','tmap_category'],
  data() {
    const validateImg = (rule, value, callback) => {
      if (this.form.fileList.length == 0) {
        callback(new Error('请上传讲解词文档'))
      } else {
        callback()
      }
    }
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/guide/user/upFiles", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 遮罩层
      loading: true,
      active: '1',
       // 表单参数
      form: {
        fileList:[]
      },
      lableList:[],
      scenicInfo: {}, // 添加景区基本信息
      scenicBoundaryPolygon: null, // 添加景区边界多边形
      // 表单校验
      rules: {
        fileList: [
          { required: true, validator: validateImg, trigger: "change" },
        ],
      },
      isEdit:false,
      form1:{},
      // 表单校验
      rules1: {
        labelName: [
          { required: true, message: "景点名称不能为空", trigger: "blur" },
        ],
        radius: [
          { required: true, message: "请输入触发半径", trigger: "blur" },
        ],
        intervalTime: [
          { required: true, message: "请输入复讲间隔时间", trigger: "blur" },
        ],
        joinCluster: [
          { required: true, message: '请选择是否聚合', trigger: 'change' }
        ],
        repetition: [
          { required: true, message: '请选择是否重复讲解', trigger: 'change' }
        ],
        showStatus: [
          { required: true, message: '请选择是佛显示', trigger: 'change' }
        ],
        explainStatus: [
          { required: true, message: '请选择是否讲解', trigger: 'change' }
        ],
        customizeCategoryCode: [
          { required: true, message: '请选择自有POI分类', trigger: 'change' }
        ],
        categoryCode: [
          { required: true, message: '请选择自有POI分类', trigger: 'change' }
        ],
      },
      allDisabled: false,
      isDraw:false,
      isAdd:false,
      isUpdatingPosition: false,
      originalPosition: null, // 保存原始位置信息
      mapType: 'vector', // 地图类型：vector-矢量图，satellite-卫星图
      clusterMarker: null, // 聚合标记对象
      currentZoom: 17, // 当前缩放级别
    }
  },
  watch:{
    $route(route) {
      if(route.query.id && route.query.id !== this.form.scenicId){
        // 清理旧数据
        this.clearOldData()
        // 设置新的景区ID
        this.form.scenicId = this.$route.query && this.$route.query.id;
        if(this.form.scenicId){
          this.getData()
          this.getLabelList()
          this.getScenicInfo()
        }
      }
    }
  },
  created(){
    this.form.scenicId = this.$route.query && this.$route.query.id;
    if(this.form.scenicId){
      this.getData()
      this.getLabelList()
      this.getScenicInfo()
    }
  },
  beforeDestroy(){
    // 组件销毁时清理数据
    this.clearOldData()
  },
  methods:{
    // 清理旧数据
    clearOldData(){
      this.form.fileList = []
      this.lableList = []
      this.scenicInfo = {}
      this.form1 = {}
      this.isEdit = false
      this.isAdd = false
      this.isDraw = false
      this.isUpdatingPosition = false
      this.allDisabled = false
      this.originalPosition = null
      this.active = '1' // 重置标签页状态
      this.mapType = 'vector' // 重置地图类型
      this.currentZoom = 17
      // 清理地图相关对象
      if(map) {
        map.destroy()
        map = null
      }
      if(marker) {
        marker = null
      }
      if(polygon) {
        polygon = null
      }
      if(editor) {
        editor = null
      }
      if(this.scenicBoundaryPolygon) {
        this.scenicBoundaryPolygon.setMap(null)
        this.scenicBoundaryPolygon = null
      }
      if(this.clusterMarker) {
        this.clusterMarker.setMap(null)
        this.clusterMarker = null
      }
    },
    // 验证经纬度是否有效
    isValidLatLng(lat, lng) {
      // 检查是否为数字
      if (typeof lat !== 'number' || typeof lng !== 'number') {
        return false
      }
      // 检查纬度范围 [-90, 90]
      if (lat < -90 || lat > 90) {
        return false
      }
      // 检查经度范围 [-180, 180]
      if (lng < -180 || lng > 180) {
        return false
      }
      return true
    },
    // 安全创建LatLng对象
    createSafeLatLng(lat, lng, fallbackLat = 39.908823, fallbackLng = 116.397470) {
      if (this.isValidLatLng(lat, lng)) {
        return new TMap.LatLng(lat, lng)
      } else {
        console.warn(`无效的经纬度: lat=${lat}, lng=${lng}, 使用默认坐标`)
        return new TMap.LatLng(fallbackLat, fallbackLng)
      }
    },
    validateInput(value,name) {
      // 使用正则表达式限制只能输入数字和小数点，且小数点后最多两位数
      this.form1[name] =  value.replace(/[^\d]/g, '')
    },
    // 获取景区基本信息
    getScenicInfo(){
      if(this.form.scenicId){
        getScenicSpot({id: this.form.scenicId}).then(res => {
          this.scenicInfo = res.data || {}
        }).catch(err => {
          console.error('获取景区信息失败:', err)
        })
      }
    },
    getLabelList(){
      getLabels({touristId:this.form.scenicId}).then(res=>{
        this.lableList = res.data
        // 如果地图已初始化，更新标记显示
        if (map) {
          this.updateMarkerDisplay()
        }
      })
    },
    getData(){
      getScenicDoc({scenicId:this.form.scenicId}).then(res=>{
        this.form.fileList = res.data.map(item =>{
          item.name = item.fileName
          return item
        })
      })
    },
    examineLabel(row){
      this.allDisabled = true
      this.isEdit = true
      this.form1 = row
      this.form1.customizeCategoryCode = (row.customizeCategoryCode == null || row.customizeCategoryCode === '') ? '-1' : String(row.customizeCategoryCode)
      this.form1.categoryCode = String(row.categoryCode)
    },
    toggleEdit(row) {
      this.isEdit = true
      this.form1 = row
      this.form1.customizeCategoryCode = (row.customizeCategoryCode == null || row.customizeCategoryCode === '') ? '-1' : String(row.customizeCategoryCode)
      this.form1.categoryCode = String(row.categoryCode)
    },
    // 删除景点
    handleDelete(row){
      delLabel({id:row.id}).then(res=>{
        this.getLabelList()
      })
    },
    mapClick(evt){
      // 检查景区是否有边界
      if (!this.hasScenicBoundary()) {
        this.$modal.msgError('请先绘制景区边界！');
        return;
      }
      
      // 检查点击位置是否在景区边界内
      if (!this.isPointInScenicBoundary(evt.latLng.lat, evt.latLng.lng)) {
        this.$modal.msgError('只能在景区边界内标点！');
        return;
      }
      
      this.form1.latitude = evt.latLng.lat
      this.form1.longitude = evt.latLng.lng
      
      // 如果是位置更新模式，显示临时marker
      if(this.isUpdatingPosition) {
        // 添加临时marker显示新选择的位置
        let currentMarkers = marker.getGeometries()
        let tempMarker = {
          id: -1,
          styleId: 'myStyle',
          position: evt.latLng
        }
        // 移除之前的临时marker（如果存在）
        currentMarkers = currentMarkers.filter(item => item.id != -1)
        // 添加新的临时marker
        currentMarkers.push(tempMarker)
        marker.setGeometries(currentMarkers)
      } else {
        // 原有的逻辑
        marker.updateGeometries({
          id: -1,
          position: evt.latLng,
        })
      }
    },
    changeDraw(type){
      if(type == 1){
        this.isDraw = true
        map.off("click",this.mapClick);
        if(editor)editor.enable() 
        if(!polygon){
          this.startDrawing()
        }
      }else{
        this.isDraw = false
        map.on("click",this.mapClick);
        if(editor)editor.disable() 
      }
    },
    addLabel(){
      // 检查景区是否有边界
      if (!this.hasScenicBoundary()) {
        this.$modal.msgError('请先绘制景区边界！');
        return;
      }
      
      this.isEdit = true
      this.isAdd = true
      this.isDraw = false
      this.form1 = {
        touristId:this.form.scenicId,
        polygon:''
      }
      // 清理之前的临时marker（id为-1）
      if(marker){
        let markers = marker.getGeometries().filter(item => item.id != -1)
        marker.setGeometries(markers)
      }
      this.reloadMap(this.form1.polygon)
      //监听点击事件添加marker
      map.on("click",this.mapClick);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log(this.form)
          saveOrUpdate(this.form.fileList).then(response => {
            this.$modal.msgSuccess('操作成功');
            this.getData()
          });
        }
      });
    },
    /** 提交按钮 */
    submitForm1: function() {
      if(this.allDisabled){
        this.form1.staus = 1
        checkLabel(this.form1).then(response => {
          this.$modal.msgSuccess('操作成功');
          this.getLabelList()
          this.isEdit = false  
          this.allDisabled = false
        });
      }else{
        this.$refs["form1"].validate(valid => {
          if (valid) {
            if(!this.form1.latitude){
              this.$modal.msgError('请在地图上标记景点位置');
              return
            }
            
            // 检查景区是否有边界
            if (!this.hasScenicBoundary()) {
              this.$modal.msgError('请先绘制景区边界！');
              return;
            }
            
            // 检查景点位置是否在景区边界内
            if (!this.isPointInScenicBoundary(this.form1.latitude, this.form1.longitude)) {
              this.$modal.msgError('景点位置必须在景区边界内！');
              return;
            }
            
            // 添加调试日志
            console.log('提交的form1数据:', this.form1)
            console.log('经纬度信息:', {
              latitude: this.form1.latitude,
              longitude: this.form1.longitude
            })
            
            if(this.form1.id){
              editLabel(this.form1).then(response => {
                this.$modal.msgSuccess('操作成功');
                this.getLabelList()
                this.isEdit = false  
                if(this.active == 2){
                  if(editor) editor.disable()  
                }
              });
            }else{
              createLabel(this.form1).then(response => {
                this.$modal.msgSuccess('操作成功');
                // 清理临时marker（id为-1）
                if(marker){
                  let markers = marker.getGeometries().filter(item => item.id != -1)
                  marker.setGeometries(markers)
                }
                // 移除地图点击事件监听
                map.off("click",this.mapClick)
                // 先刷新列表数据
                this.getLabelList()
                // 然后重新渲染地图
                setTimeout(() => {
                  this.reloadMap()                  
                }, 300);
                this.isEdit = false  
                this.isDraw = false
                this.isAdd = false
                if(this.active == 2){
                  if(editor) editor.disable()
                }
              });
            }
          }
        })
      }
    },
    cancel1(){
      this.form1 = {}
      this.isEdit = false   
      this.isAdd = false
      this.isUpdatingPosition = false
      this.allDisabled = false  // 重置禁用状态
      this.originalPosition = null
      if(editor)editor.disable() 
      this.getLabelList()
      if(marker){
        let markers = marker.getGeometries().filter(item => item.id != -1)
        marker.setGeometries(markers)
        map.off("click",this.mapClick)
      }
      if(polygon){
        polygon.setMap(null)
        polygon = null
        editor.removeOverlay('polygon')
      }
      // 重新渲染景区边界
      if(this.scenicBoundaryPolygon) {
        this.scenicBoundaryPolygon.setMap(null)
        this.scenicBoundaryPolygon = null
      }
      this.renderScenicBoundary()
    },
    handleClick(tab){
      if(tab.name == 2){
        setTimeout(() => {
          this.initMap()          
        }, 100);
      }else{
        if(map) map.destroy()
        // 清理景区边界多边形
        if(this.scenicBoundaryPolygon) {
          this.scenicBoundaryPolygon.setMap(null)
          this.scenicBoundaryPolygon = null
        }
        // 清理聚合标记
        if(this.clusterMarker) {
          this.clusterMarker.setMap(null)
          this.clusterMarker = null
        }
      }
      this.isEdit = false
      this.form1 = {}
      this.allDisabled = false  // 重置禁用状态
      this.getLabelList()      
    },
    // 取消按钮
    cancel() {
      this.$router.push({
        path: '/cooperate/guide'
      })
    },
    handleUploadSuccess(res, file,fileList) {
      console.log(fileList);
      this.$refs.form.clearValidate('fileList')
      this.form.fileList = fileList.map(item => {
        return {
          scenicId: this.form.scenicId,
          url: item.response && item.response.data,
          fileName: item.name,
          name: item.name,
          id: item.id
        }
      });
    },
    handleUploadRemove(file,fileList){
      console.log(file,'file...');
      if(file.id){
        delOrUpdate({id:file.id})
      }
      this.form.fileList = fileList.map(item => {
        return {
          scenicId: this.form.scenicId,
          url: item.response && item.response.data,
          fileName: item.name,
          name: item.name,
          id: item.id
        }
      });
    },
    // 初始化地图
    initMap(){
      // 确定地图中心点坐标 - 始终使用景区的经纬度作为中心点
      let center
      let zoom = 17 // 默认缩放级别
      
      // 优先使用景区的经纬度
      if(this.scenicInfo && this.scenicInfo.latitude && this.scenicInfo.longitude){
        console.log('使用景区经纬度作为中心点:', this.scenicInfo.latitude, this.scenicInfo.longitude)
        // 注意：API返回的latitude实际是经度，longitude实际是纬度
        center = this.createSafeLatLng(
          parseFloat(this.scenicInfo.longitude), // 纬度
          parseFloat(this.scenicInfo.latitude)   // 经度
        )
        
        // 根据是否有边界和讲解点调整缩放级别
        const hasBoundary = this.scenicInfo.polygonList && this.scenicInfo.polygonList.length > 0
        const hasLabels = this.lableList && this.lableList.length > 0
        
        if(hasBoundary && hasLabels) {
          zoom = 16 // 有边界和讲解点，稍微缩小一点
        } else if(hasBoundary || hasLabels) {
          zoom = 16.5 // 只有边界或只有讲解点
        } else {
          zoom = 17 // 只有景区中心点
        }
      }
      // 如果没有景区经纬度，使用默认坐标（北京天安门）
      else{
        console.log('使用默认坐标作为中心点')
        center = this.createSafeLatLng(39.908823, 116.397470)
      }
      
      // 根据地图类型设置不同的地图配置
      const mapConfig = {
        center: center,//设置地图中心点坐标
        zoom: zoom,   //设置地图缩放级别
        viewMode: '2D',
        showControl: false,
        baseMap: { type: this.mapType === 'satellite' ? 'satellite' : 'vector' }
      }
      
      // 根据地图类型添加不同的样式配置
      if (this.mapType === 'satellite') {
        // 卫星图配置 - 使用腾讯地图WebGL API的卫星图样式
        mapConfig.styleId = 'satellite'
      } else {
        // 矢量图配置 - 使用腾讯地图WebGL API的普通样式
        mapConfig.styleId = 'normal'
      }
      
      map = new TMap.Map(document.getElementById('map'), mapConfig)
      this.markerLayer() 
      this.editorPolygonLayer()
      this.renderScenicBoundary() // 添加景区边界渲染
      
      // 监听地图缩放事件，实现自动聚合
      map.on('zoom', this.handleMapZoom)
      map.on('move', this.handleMapMove)
      
      // 初始化聚合显示
      this.updateMarkerDisplay()
    },
    //标记地图
    markerLayer(){
      let list = this.lableList || []
      console.log('渲染讲解点，讲解点数量:', list.length)
      
      marker = new TMap.MultiMarker({
        map: map, //指定地图容器
        //样式定义
        styles: {
          //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
          "myStyle": new TMap.MarkerStyle({ 
            "width": 25,  // 点标记样式宽度（像素）
            "height": 35, // 点标记样式高度（像素）
            // "src": '../img/marker.png',  //图片路径
            background:'pink',
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            "anchor": { x: 16, y: 32 }  
          }),
        },
        // 设置点标记数据
        geometries: list.map(item=>{
          return {
            "id": item.id,   //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            "styleId": 'myStyle',  //指定样式id
            "position": this.createSafeLatLng(
              parseFloat(item.latitude), 
              parseFloat(item.longitude)
            ),  //点标记坐标位置
          }
        }),
      })
      marker.on('click',this.markerClickHandler)
    },
    //监听回调函数（非匿名函数）
    markerClickHandler(e) {
      if(e.geometry.id == -1) return
      let markers = marker.getGeometries().filter(item => item.id != -1)
      marker.setGeometries(markers)
      map.off("click",this.mapClick)
      
      // 如果当前正在更新位置，不要重新加载数据
      if(this.isUpdatingPosition) {
        return
      }
      
      this.form1 = this.lableList.filter(item=> item.id == e.geometry.id)[0]
      this.isEdit = true
      this.isDraw = !!this.form1.polygon
      this.isAdd = false
      this.reloadMap(this.form1.polygon)
    },
    //多边形绘制
    editorPolygonLayer(){
      editor = new TMap.tools.GeometryEditor({
        map: map, // 编辑器绑定的地图对象
        actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW, // 编辑器的工作模式
        activeOverlayId: 'polygon', // 激活图层
        snappable: true, // 开启吸附
      }) 
      editor.disable()
      // 监听绘制结束事件，获取绘制几何图形
      editor.on('draw_complete', (geometry) => {
        // 判断当前处于编辑状态的图层id是否是overlayList中id为polygon（矩形）图层
        if (editor.getActiveOverlay().id === 'polygon') {
          // 获取矩形顶点坐标
          var id = geometry.id;
          var geo = polygon.geometries.filter(function (item) {
              return item.id === id;
          });
          let polygonStr = JSON.parse(JSON.stringify(geo[0].paths)).map(item=>{
            return `${item.lat},${item.lng}`
          }).join(';')
          this.$set(this.form1,'polygon',polygonStr)
          editor.disable()
        }
      });
    },
    // 更新地图
    reloadMap(polygons, useForm1Data = false){
      let list = this.lableList || []
      
      // 如果指定使用form1数据，则使用当前编辑的数据
      if(useForm1Data && this.form1 && this.form1.id) {
        // 创建包含当前编辑数据的列表
        list = list.map(item => {
          if(item.id === this.form1.id) {
            return { ...item, ...this.form1 }
          }
          return item
        })
      }
      
      // 根据当前缩放级别更新标记显示
      this.updateMarkerDisplay()
      
      if (polygon) {
        editor.removeOverlay('polygon')
        polygon.setMap(null);
        polygon = null 
      }
      if(polygons){
        polygon = new TMap.MultiPolygon({
          map: map,
          geometries:[{
            id:1,
            paths: polygons.split(';').map(item=>{
              const [lat, lng] = item.split(',')
              return this.createSafeLatLng(parseFloat(lat), parseFloat(lng))
            }),
          }]
        });
      }else{
        polygon = new TMap.MultiPolygon({
          map: map
        })
      }
      editor.addOverlay({
        overlay: polygon,
        id: 'polygon',
      })
      editor.disable()
    },
    // 重新配置区域
    resetEditor(){
      map.off("click",this.mapClick);
      this.form1.polygon = ''
      polygon.setMap(null)
      polygon = null
      editor.removeOverlay('polygon')
      // polygon.setGeometries([])
      editor.enable() 
      this.startDrawing()
    },
    startDrawing() {
      polygon = new TMap.MultiPolygon({
        map: map
      })
      editor.addOverlay({
        overlay: polygon,
        id: 'polygon',
      })
    },
    // 渲染景区边界
    renderScenicBoundary(){
      // 如果景区信息中有边界数据，则渲染景区边界
      if(this.scenicInfo && this.scenicInfo.polygonList && this.scenicInfo.polygonList.length > 0){
        console.log('渲染景区边界，边界点数量:', this.scenicInfo.polygonList.length)
        // 创建景区边界多边形
        const scenicPolygon = new TMap.MultiPolygon({
          map: map,
          styles: {
            'scenicBoundary': new TMap.PolygonStyle({
              color: 'rgba(0, 123, 255, 0.3)', // 半透明蓝色填充
              showBorder: true,
              borderColor: '#007bff', // 蓝色边框
              borderWidth: 2
            })
          },
          geometries: [{
            id: 'scenic-boundary',
            styleId: 'scenicBoundary',
            paths: this.scenicInfo.polygonList.map(item => {
              const [lat, lng] = item.split(',')
              return this.createSafeLatLng(parseFloat(lat), parseFloat(lng))
            })
          }]
        })
        
        // 将景区边界多边形保存到全局变量，方便后续管理
        this.scenicBoundaryPolygon = scenicPolygon
      } else {
        console.log('景区无边界数据，跳过边界渲染')
      }
    },
    startUpdatePosition() {
      // 检查景区是否有边界
      if (!this.hasScenicBoundary()) {
        this.$modal.msgError('请先绘制景区边界！');
        return;
      }
      
      this.isUpdatingPosition = true
      // 保存原始位置信息
      this.originalPosition = {
        latitude: this.form1.latitude,
        longitude: this.form1.longitude
      }
      // 添加临时marker用于位置选择
      if(marker){
        let markers = marker.getGeometries().filter(item => item.id != -1)
        marker.setGeometries(markers)
      }
      // 监听地图点击事件
      map.on("click", this.mapClick)
    },
    confirmUpdatePosition() {
      if(!this.form1.latitude || !this.form1.longitude) {
        this.$modal.msgError('请先选择新的位置')
        return
      }
      
      // 添加调试日志
      console.log('确认位置更新，新的经纬度:', {
        latitude: this.form1.latitude,
        longitude: this.form1.longitude
      })
      
      this.isUpdatingPosition = false
      // 移除地图点击事件监听
      map.off("click", this.mapClick)
      // 清理临时marker
      if(marker){
        let markers = marker.getGeometries().filter(item => item.id != -1)
        marker.setGeometries(markers)
      }
      // 重新加载地图显示更新后的位置，使用form1的数据
      this.reloadMap(this.form1.polygon, true)
      this.$modal.msgSuccess('位置更新成功，请点击确定保存')
    },
    cancelUpdatePosition() {
      this.isUpdatingPosition = false
      // 恢复原始位置
      if(this.originalPosition) {
        this.form1.latitude = this.originalPosition.latitude
        this.form1.longitude = this.originalPosition.longitude
      }
      // 移除地图点击事件监听
      map.off("click", this.mapClick)
      // 清理临时marker
      if(marker){
        let markers = marker.getGeometries().filter(item => item.id != -1)
        marker.setGeometries(markers)
      }
      // 重新加载地图显示原始位置，使用form1的数据
      this.reloadMap(this.form1.polygon, true)
      this.originalPosition = null
    },
    // 切换地图类型
    changeMapType() {
      if (map) {
        // 保存当前地图状态
        const currentCenter = map.getCenter()
        const currentZoom = map.getZoom()
        
        // 销毁当前地图
        map.destroy()
        
        // 重新初始化地图
        this.$nextTick(() => {
          this.initMap()
          
          // 恢复地图状态
          if (map && currentCenter && currentZoom) {
            map.setCenter(currentCenter)
            map.setZoom(currentZoom)
          }
        })
      }
    },
    // 处理地图缩放事件
    handleMapZoom() {
      this.currentZoom = map.getZoom()
      this.updateMarkerDisplay()
    },
    // 处理地图移动事件
    handleMapMove() {
      this.updateMarkerDisplay()
    },
    // 更新标记显示
    updateMarkerDisplay() {
      if (!map || !this.lableList || this.lableList.length === 0) return
      
      const zoom = map.getZoom()
      
      // 根据缩放级别决定是否聚合
      if (zoom >= 16) {
        // 高缩放级别，显示单个标记
        this.showIndividualMarkers()
      } else {
        // 低缩放级别，显示聚合标记
        this.showClusterMarkers()
      }
    },
    // 显示单个标记
    showIndividualMarkers() {
      // 隐藏聚合标记
      if (this.clusterMarker) {
        this.clusterMarker.setMap(null)
        this.clusterMarker = null
      }
      
      // 显示原始标记
      if (marker) {
        marker.setMap(map)
      }
    },
    // 显示聚合标记
    showClusterMarkers() {
      // 隐藏原始标记
      if (marker) {
        marker.setMap(null)
      }
      
      // 创建或更新聚合标记
      this.createOrUpdateClusterMarkers()
    },
    // 创建或更新聚合标记
    createOrUpdateClusterMarkers() {
      if (!this.clusterMarker) {
        this.createClusterMarker()
      }
      
      const clusters = this.performClustering()
      this.updateClusterGeometries(clusters)
    },
    // 创建聚合标记
    createClusterMarker() {
      this.clusterMarker = new TMap.MultiMarker({
        map: map,
        styles: {
          'cluster': new TMap.MarkerStyle({
            width: 40,
            height: 40,
            anchor: { x: 20, y: 20 },
            background: '#ff6b6b',
            borderRadius: '50%',
            border: '2px solid #fff',
            boxShadow: '0 2px 6px rgba(0,0,0,0.3)'
          }),
          'single': new TMap.MarkerStyle({
            width: 25,
            height: 35,
            background: 'pink',
            anchor: { x: 16, y: 32 }
          })
        }
      })
      
      // 监听聚合标记点击事件
      this.clusterMarker.on('click', this.clusterClickHandler)
    },
    // 聚合标记点击处理
    clusterClickHandler(e) {
      const geometry = e.geometry
      
      if (geometry.clusterData && geometry.clusterData.length > 1) {
        // 聚合点被点击，展开聚合
        this.expandCluster(geometry)
      } else if (geometry.clusterData && geometry.clusterData.length === 1) {
        // 单个点被点击，执行原有逻辑
        this.handleSingleMarkerClick(geometry.clusterData[0])
      }
    },
    // 展开聚合
    expandCluster(clusterGeometry) {
      const clusterData = clusterGeometry.clusterData
      
      // 计算聚合点的边界
      const bounds = this.calculateBounds(clusterData)
      
      // 调整地图视野以显示所有点
      map.fitBounds(bounds, {
        padding: 50
      })
      
      // 更新聚合显示
      setTimeout(() => {
        this.updateMarkerDisplay()
      }, 300)
    },
    // 处理单个标记点击
    handleSingleMarkerClick(markerData) {
      if (markerData.id == -1) return
      
      // 移除地图点击事件监听
      map.off("click", this.mapClick)
      
      // 如果当前正在更新位置，不要重新加载数据
      if (this.isUpdatingPosition) {
        return
      }
      
      this.form1 = this.lableList.filter(item => item.id == markerData.id)[0]
      this.isEdit = true
      this.isDraw = !!this.form1.polygon
      this.isAdd = false
      this.reloadMap(this.form1.polygon)
    },
    // 计算边界
    calculateBounds(markers) {
      if (!markers || markers.length === 0) return null
      
      let minLat = Infinity, maxLat = -Infinity
      let minLng = Infinity, maxLng = -Infinity
      
      markers.forEach(marker => {
        const lat = parseFloat(marker.latitude)
        const lng = parseFloat(marker.longitude)
        
        minLat = Math.min(minLat, lat)
        maxLat = Math.max(maxLat, lat)
        minLng = Math.min(minLng, lng)
        maxLng = Math.max(maxLng, lng)
      })
      
      return {
        northEast: new TMap.LatLng(maxLat, maxLng),
        southWest: new TMap.LatLng(minLat, minLng)
      }
    },
    // 执行聚合算法
    performClustering() {
      const zoom = map.getZoom()
      const clusterRadius = this.getClusterRadius(zoom)
      const markers = this.lableList || []
      const clusters = []
      const processed = new Set()
      
      markers.forEach((marker, index) => {
        if (processed.has(index)) return
        
        const cluster = {
          center: new TMap.LatLng(parseFloat(marker.latitude), parseFloat(marker.longitude)),
          markers: [marker],
          count: 1
        }
        
        processed.add(index)
        
        // 查找附近的标记
        for (let i = index + 1; i < markers.length; i++) {
          if (processed.has(i)) continue
          
          const otherMarker = markers[i]
          const distance = this.calculateDistance(
            parseFloat(marker.latitude), parseFloat(marker.longitude),
            parseFloat(otherMarker.latitude), parseFloat(otherMarker.longitude)
          )
          
          if (distance <= clusterRadius) {
            cluster.markers.push(otherMarker)
            cluster.count++
            processed.add(i)
          }
        }
        
        // 计算聚合中心点
        if (cluster.count > 1) {
          const avgLat = cluster.markers.reduce((sum, m) => sum + parseFloat(m.latitude), 0) / cluster.count
          const avgLng = cluster.markers.reduce((sum, m) => sum + parseFloat(m.longitude), 0) / cluster.count
          cluster.center = new TMap.LatLng(avgLat, avgLng)
        }
        
        clusters.push(cluster)
      })
      
      return clusters
    },
    // 根据缩放级别获取聚合半径
    getClusterRadius(zoom) {
      // 缩放级别越大，聚合半径越小
      if (zoom >= 15) return 80 // 小聚合
      if (zoom >= 13) return 120 // 中等聚合
      return 200 // 大聚合
    },
    // 计算两点间距离（像素）
    calculateDistance(lat1, lng1, lat2, lng2) {
      const point1 = map.projectToContainer(new TMap.LatLng(lat1, lng1))
      const point2 = map.projectToContainer(new TMap.LatLng(lat2, lng2))
      
      return Math.sqrt(Math.pow(point1.x - point2.x, 2) + Math.pow(point1.y - point2.y, 2))
    },
    // 更新聚合几何体
    updateClusterGeometries(clusters) {
      const geometries = []
      
      clusters.forEach((cluster, index) => {
        if (cluster.count === 1) {
          // 单个标记
          geometries.push({
            id: `cluster-${index}`,
            styleId: 'single',
            position: cluster.center,
            clusterData: cluster.markers
          })
        } else {
          // 聚合标记
          geometries.push({
            id: `cluster-${index}`,
            styleId: 'cluster',
            position: cluster.center,
            clusterData: cluster.markers,
            label: {
              content: cluster.count.toString(),
              direction: 'center',
              offset: { x: 0, y: 0 },
              style: {
                color: '#fff',
                fontSize: '14px',
                fontWeight: 'bold'
              }
            }
          })
        }
      })
      
      this.clusterMarker.setGeometries(geometries)
    },
    // 检查景区是否有边界
    hasScenicBoundary() {
      return this.scenicInfo && this.scenicInfo.polygonList && this.scenicInfo.polygonList.length > 0;
    },
    
    // 判断点是否在景区边界内（射线法）
    isPointInScenicBoundary(lat, lng) {
      if (!this.hasScenicBoundary()) {
        return false;
      }
      
      const polygonPoints = this.scenicInfo.polygonList.map(item => {
        const [pointLat, pointLng] = item.split(',');
        return {
          lat: parseFloat(pointLat),
          lng: parseFloat(pointLng)
        };
      });
      
      return this.isPointInPolygon(lat, lng, polygonPoints);
    },
    
    // 射线法判断点是否在多边形内
    isPointInPolygon(lat, lng, polygon) {
      let inside = false;
      const x = lng;
      const y = lat;
      
      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = polygon[i].lng;
        const yi = polygon[i].lat;
        const xj = polygon[j].lng;
        const yj = polygon[j].lat;
        
        if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
          inside = !inside;
        }
      }
      
      return inside;
    },
    
    // 跳转到景区管理页面
    goToScenicManagement() {
      this.$router.push({
        path: '/cooperate/guide/add',
        query: { id: this.form.scenicId }
      });
    },
  }
}
</script>

<style>
  .store_map{
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
  
  .map-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
  
  .map-controls .el-radio-group {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2px;
  }
  
  .boundary-warning {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #f39c12;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .boundary-warning .el-button {
    background-color: #f39c12;
    border-color: #f39c12;
  }
  
  .boundary-warning .el-button:hover {
    background-color: #e67e22;
    border-color: #e67e22;
  }
</style>