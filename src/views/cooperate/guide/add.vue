<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card style="margin-bottom:20px">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
          <div style="float:right" v-if='this.form.id'>
            <el-button size='mini' type="primary" @click="submitForm">保存</el-button>
            <el-button size='mini' @click="cancel">取 消</el-button>
          </div>
        </div>
        <el-row>
          <el-col :span="10">
            <el-form-item label="景区名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入景区名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="景区地址" required>
              <el-col :span="4">
                <el-form-item prop="provinceCode">
                  <el-select v-model="form.provinceCode" placeholder="请选择省" @change="e => handleChange(e,1)" style="width:100%;padding-right: 12px;">
                    <el-option
                      v-for="item in provinceList"
                      :key="item.adcode"
                      :label="item.name"
                      :value="item.adcode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item prop="cityCode">
                  <el-select v-model="form.cityCode" placeholder="请选择市" @change="e => handleChange(e,2)" style="width:100%;padding-right: 12px;">
                    <el-option
                      v-for="item in cityList"
                      :key="item.adcode"
                      :label="item.name"
                      :value="item.adcode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item prop="districtCode">
                  <el-select v-model="form.districtCode" placeholder="请选择区" @change="e => handleChange(e,3)" style="width:100%;padding-right: 12px;">
                    <el-option
                      v-for="item in districtList"
                      :key="item.adcode"
                      :label="item.name"
                      :value="item.adcode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="detailAddress">
                  <el-input v-model="form.detailAddress" placeholder="请输入详细地址" @blur='querySearchMap'/>                
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="景区负责人" prop="kahuna">
              <el-input v-model="form.kahuna" placeholder="请输入景区负责人"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="联系电话" prop="contactNumber">
              <el-input v-model="form.contactNumber" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="景区等级" prop="level">
              <el-select
                v-model="form.level"
                placeholder="等级"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.guide_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="景区缩略图" prop="thumbnail">
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload">
                <img v-if="form.thumbnail" :src="form.thumbnail" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <span style="color:red">注：文件格式必须为jpg、png，大小128 x 128px</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="景区导览图" prop="guideMap">
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSucces1"
                :before-upload="beforeAvatarUpload">
                <img v-if="form.guideMap" :src="form.guideMap" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <span style="color:red">注：文件格式必须为jpg、png</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="景区上线状态" prop="status">
              <el-switch
                v-model="form.status"
                active-value="0"
                inactive-value="1"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-bottom:20px" class="store_map">
        <div slot="header" class="clearfix">
          <span>创建景区边界</span>
        </div>
        <div class="map-controls" style="margin-bottom: 10px;">
          <el-radio-group v-model="mapType" @change="changeMapType" size="small">
            <el-radio-button label="vector">矢量图</el-radio-button>
            <el-radio-button label="satellite">卫星图</el-radio-button>
          </el-radio-group>
        </div>
        <div id="container" style="width:800px;height:450px;margin-left:6%"></div>
				<el-button type="primary" style="margin-left:20px" @click="resetEditor">重新配置区域</el-button>
				<div style="display:block;margin-left:6%">
					<div>绘制:鼠标左键点击及移动即可绘制图形，绘制线段不可交叉，否则景区边界(轮廓)无法闭环，仅支持一个景区边界(轮廓)的绘制;</div>
					<div>结束绘制:鼠标左键双击即可结束绘制，折线、多边形会自动闭合;</div>
					<div>中断:绘制过程中按下esc键可中断该过程;</div>
				</div>
      </el-card>
    </el-form>
    <div style="text-align:center" v-if='!this.form.id'>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { district } from "@/api/cooperate/institution";
import { addScenicSpot, getScenicSpot, editScenicSpot } from "@/api/cooperate/guide";
import { getToken } from "@/utils/auth";
let map,marker,polygon,editor
export default {
  name: "guideAdd",
  dicts: ['guide_level'],
  data() {
    const validateImg = (rule, value, callback) => {
      if (!this.form.thumbnail) {
        callback(new Error('请上传缩略图'))
      } else {
        callback()
      }
    }
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/guide/user/upFiles", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 遮罩层
      loading: true,
       // 表单参数
      form: {
        status: '0',
        provinceCode: '',
        provinceName:'',
        cityCode: '',
        cityName:'',
        districtCode: '',
        districtName:'',
        thumbnail: '',
        guideMap: '',
        polygon:'',
        latitude:'39.984120',//纬度
        longitude:'116.307484',//经度
      },
      provinceList: [],
      cityList: [],
      districtList: [],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "景区名称不能为空", trigger: "blur" },
        ],
        provinceCode: [
          { required: true, message: '请选择省', trigger: 'change' }
        ],
        cityCode: [
          { required: true, message: '请选择市', trigger: 'change' }
        ],
        districtCode: [
          { required: true, message: '请选择区', trigger: 'change' }
        ],
        kahuna: [
          { required: true, message: "景区负责人不能为空", trigger: "blur" }
        ],
        detailAddress: [
          { required: true, message: "详细地址不能为空", trigger: "blur" },
        ],
        contactNumber: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        level: [
          { required: true, message: "请选择景区等级", trigger: "change" }
        ],
        thumbnail: [
          { required: true, validator: validateImg, trigger: "change" },
        ],
        status: [
          { required: true, message: "请选择上线状态", trigger: "blur" }
        ],
      },
      mapType: 'vector',
    }
  },
  watch:{
    $route(route) {
      if(route.query.id && route.query.id !== this.form.id){
        this.getProvince('100000',0)
        this.form.id = this.$route.query && this.$route.query.id;
        if(this.form.id){
          this.getData()
        }
      }
    }
  },
  created(){
    this.getProvince('100000',0)
    this.form.id = this.$route.query && this.$route.query.id;
    if(this.form.id){
      this.getData()
    }
  },
  mounted(){
    this.initMap()//初始化地图
  },
  onBeforeUnmount(){
    map.destroy()
  },
  methods:{
    // 初始化地图
    initMap(){
      //定义地图中心点坐标
      let center = new TMap.LatLng(this.form.latitude, this.form.longitude)
      const mapConfig = {
        center: center,//设置地图中心点坐标
        zoom: 17,   //设置地图缩放级别
        viewMode: '2D',
        showControl: false,
        baseMap: { type: this.mapType === 'satellite' ? 'satellite' : 'vector' }
      }
      map = new TMap.Map(document.getElementById('container'), mapConfig)
      this.markerLayer() 
      this.editorPolygonLayer()
    },
    //标记地图
    markerLayer(){
      marker = new TMap.MultiMarker({
        map: map, //指定地图容器
        //样式定义
        styles: {
          //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
          "myStyle": new TMap.MarkerStyle({ 
            "width": 25,  // 点标记样式宽度（像素）
            "height": 35, // 点标记样式高度（像素）
            // "src": '../img/marker.png',  //图片路径
            background:'pink',
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            "anchor": { x: 16, y: 32 }  
          }) 
        },
        // 设置点标记数据
        geometries: [{
          "id": "1",   //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          "styleId": 'myStyle',  //指定样式id
          "position": new TMap.LatLng(this.form.latitude, this.form.longitude),  //点标记坐标位置
        }],
      })
    },
    //多边形绘制
    editorPolygonLayer(){
      polygon = new TMap.MultiPolygon({
        map: map,
      }) 
      editor = new TMap.tools.GeometryEditor({
        map: map, // 编辑器绑定的地图对象
        overlayList: [
          {
            overlay: polygon,
            id: 'polygon',
          }
        ],
        actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW, // 编辑器的工作模式
        activeOverlayId: 'polygon', // 激活图层
        snappable: true, // 开启吸附
      }) 
      // 监听绘制结束事件，获取绘制几何图形
      editor.on('draw_complete', (geometry) => {
        // 判断当前处于编辑状态的图层id是否是overlayList中id为polygon（矩形）图层
        if (editor.getActiveOverlay().id === 'polygon') {
          // 获取矩形顶点坐标
          var id = geometry.id;
          var geo = polygon.geometries.filter(function (item) {
              return item.id === id;
          });
          // 将坐标转换为后端需要的格式：纬度,经度;纬度,经度;...
          this.form.polygon = JSON.parse(JSON.stringify(geo[0].paths)).map(item=>{
            return `${item.lat},${item.lng}`
          }).join(';')
          editor.disable()
        }
      });
    },
    // 重新配置区域
    resetEditor(){
      this.form.polygon = ''
      polygon.setGeometries([])
      editor.enable()
    },
    // 更新地图
    reloadMap(){
      map.setCenter(new TMap.LatLng(this.form.latitude, this.form.longitude))
      marker.updateGeometries([
        {
          "styleId":"marker",
          "id": "1",
          "position": new TMap.LatLng(this.form.latitude, this.form.longitude),
        }
      ])
      
      // 处理polygon数据 - 只使用form.polygon字段
      let polygonData = []
      if (this.form.polygon) {
        // 解析polygon字符串格式：纬度,经度;纬度,经度;...
        const points = this.form.polygon.split(';')
        polygonData = points.map(point => {
          const [lat, lng] = point.split(',')
          return new TMap.LatLng(parseFloat(lat), parseFloat(lng))
        })
      }
      
      polygon.setGeometries(polygonData.length > 0 ? [
        {
          "styleId":"polygon",
          "id": "1",
          "paths": polygonData,
        }
      ] : [])
      
      editor.enable()     
    },
    // 根据详细地址获取经纬度
    querySearchMap(e,address){
      let addr = this.form.provinceName + this.form.cityName + this.form.districtName + (e ? e.target.value : address)
      this.$jsonp('https://apis.map.qq.com/ws/geocoder/v1',{
        key:'E4UBZ-B3QKL-JP6PS-MY6T4-K5NI5-X4BHR',
        address:addr,
        output:'jsonp'
      }).then((res)=>{
        if(res.status == 0){
          this.form.longitude = res.result.location.lng
          this.form.latitude = res.result.location.lat
          setTimeout(()=>{
            this.reloadMap()
          },100)
        }
      })
    },
    getData(){
      getScenicSpot({id:this.form.id}).then(res=>{
        this.getProvince(res.data.provinceCode,1)
        this.getProvince(res.data.cityCode,2)
        setTimeout(()=>{
          this.form = res.data
          if (this.form.level !== undefined && this.form.level !== null) {
            this.form.level = String(this.form.level)
          }
          this.querySearchMap('',res.data.detailAddress)
        })
        setTimeout(()=>{
          // 重新加载地图，显示polygon边界
          this.reloadMap()
        },1000)
      })
    },
    // 获取省份
    getProvince(padcode,level){
      const levelList = ['provinceList','cityList','districtList']
      district({padcode:padcode}).then(res=>{ 
        this[levelList[level]] = res.data
      })
    },
    // 省市切换
    async handleChange(e,type){
      if(type == 1){
        this.form.provinceName = this.provinceList.filter(item => e == item.adcode)[0].name
        this.districtList = []
        this.form.cityCode = ''
        this.form.districtCode = ''
      }else if(type == 2){
        this.form.cityName = this.cityList.filter(item => e == item.adcode)[0].name
        this.form.districtCode = ''
      }else{
        this.form.districtName = this.districtList.filter(item => e == item.adcode)[0].name
      }
      if(type != 3) await this.getProvince(e,type)
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(!this.form.polygon){
            this.$modal.msgError('未创建景区边界!')
            return
          }
          console.log(this.form)
          if(this.form.id){
            editScenicSpot(this.form).then(response => {
              this.$modal.msgSuccess('操作成功');
            });
          }else{
            addScenicSpot(this.form).then(response => {
              this.$modal.msgSuccess('操作成功');
              this.$router.push({
                path: '/cooperate/guide'
              })
            });
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.$router.push({
        path: '/cooperate/guide'
      })
    },
    handleAvatarSuccess(res, file) {
      this.$refs.form.clearValidate('thumbnail')
      this.form.thumbnail = res.data;
    },
    handleAvatarSucces1(res, file) {
      this.$refs.form.clearValidate('guideMap')
      this.form.guideMap = res.data;
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';

      if (!isJPG && !isPNG) {
        this.$message.error('文件格式必须为jpg、png!');
      }
      return isJPG || isPNG;
    },
    changeMapType() {
      if (map) {
        // 保存当前地图状态
        const currentCenter = map.getCenter()
        const currentZoom = map.getZoom()
        
        // 销毁当前地图
        map.destroy()
        
        // 重新初始化地图
        this.$nextTick(() => {
          this.initMap()
          
          // 恢复地图状态
          if (map && currentCenter && currentZoom) {
            map.setCenter(currentCenter)
            map.setZoom(currentZoom)
          }
          
          // 重新加载polygon数据
          this.reloadMap()
        })
      }
    },
  }
}
</script>

<style>
  .store_map .el-card__body{
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
  }
  
  .map-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
  
  .map-controls .el-radio-group {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2px;
  }
  
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
</style>