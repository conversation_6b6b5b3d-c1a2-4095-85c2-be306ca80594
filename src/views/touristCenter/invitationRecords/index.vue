<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="邀请人账号" prop="inviterAccount">
        <el-input
          v-model="queryParams.inviterAccount"
          placeholder="请输入邀请人账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被邀请人账号" prop="inviteeAccount">
        <el-input
          v-model="queryParams.inviteeAccount"
          placeholder="请输入被邀请人账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邀请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="~"
          start-placeholder="请选择时间"
          end-placeholder="请选择时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--  plain -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          plain
          size="small"
          @click="handleExport"
        >导出excel</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="invitationList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="邀请人账号" align="center" prop="inviterAccount" />
      <el-table-column label="被邀请人账号" align="center" prop="inviteeAccount" />
      <el-table-column label="邀请时间" align="center" prop="invitationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.invitationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="奖励状态" align="center" prop="rewardStatus" />
      <el-table-column label="邀请状态" align="center" prop="invitationStatus" />
      <el-table-column label="邀请奖励 (游豆)" align="center" prop="rewardAmount" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "InvitationRecords",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邀请记录表格数据
      invitationList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inviterAccount: null,
        inviteeAccount: null,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询邀请记录列表 */
    getList() {
      this.loading = true;
      // 这里应该是调用API获取数据
      // 我用模拟数据代替
      setTimeout(() => {
        this.invitationList = [
          {
            id: 1,
            inviterAccount: '***********',
            inviteeAccount: '***********',
            invitationTime: '2025-03-25',
            rewardStatus: '已发放',
            invitationStatus: '已受邀',
            rewardAmount: 10
          },
          {
            id: 2,
            inviterAccount: '***********',
            inviteeAccount: '***********',
            invitationTime: '2025-02-10',
            rewardStatus: '发放失败',
            invitationStatus: '已受邀',
            rewardAmount: 10
          }
        ];
        this.total = 2; // 假设总共有2条
        this.loading = false;
      }, 1000);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/post/export', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>