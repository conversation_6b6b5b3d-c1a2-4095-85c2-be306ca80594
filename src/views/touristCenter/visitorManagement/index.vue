<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="游客账号" prop="account">
        <el-input
          v-model="queryParams.account"
          placeholder="请输入游客账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="visitorList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="游客ID" align="center" prop="visitorId" />
      <el-table-column label="游客账号" align="center" prop="touristAccount" />
      <el-table-column label="注册时间" align="center" prop="createTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "VisitorManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 游客管理表格数据
      visitorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        account: undefined,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询游客列表 */
    getList() {
      this.loading = true;
      const mockData = {
        rows: [
          { visitorId: 'XXXXXXX', touristAccount: '***********', createTime: '2024-10-12 12:10:25' },
          { visitorId: 'YYYYYYY', touristAccount: '***********', createTime: '2024-10-13 10:20:35' },
          { visitorId: 'ZZZZZZZ', touristAccount: '***********', createTime: '2024-10-14 11:30:45' },
          { visitorId: 'AAAAAAA', touristAccount: '***********', createTime: '2024-10-15 12:40:55' },
          { visitorId: 'BBBBBBB', touristAccount: '***********', createTime: '2024-10-16 13:50:05' },
          { visitorId: 'CCCCCCC', touristAccount: '***********', createTime: '2024-10-17 14:00:15' },
          { visitorId: 'DDDDDDD', touristAccount: '***********', createTime: '2024-10-18 15:10:25' },
          { visitorId: 'EEEEEEE', touristAccount: '***********', createTime: '2024-10-19 16:20:35' },
          { visitorId: 'FFFFFFF', touristAccount: '***********', createTime: '2024-10-20 17:30:45' },
          { visitorId: 'GGGGGGG', touristAccount: '***********', createTime: '2024-10-21 18:40:55' }
        ],
        total: 21
      };

     
      const screenshot_mock = {
        rows: [
            { visitorId: 'XXXXXXX', touristAccount: '***********', createTime: '2024-10-12 12:10:25' },
            ...Array(9).fill({}).map(()=>({visitorId:'', touristAccount:'', createTime:''}))
        ],
        total: 21
      }


      this.visitorList = screenshot_mock.rows;
      this.total = screenshot_mock.total;
      this.loading = false;

      /*
      
      listVisitor(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.visitorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      */
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      const visitorId = row.visitorId || '****************';
      this.$router.push({ path: "/touristCenter/visitorManagement/view/" + visitorId });
    }
  }
};
</script>