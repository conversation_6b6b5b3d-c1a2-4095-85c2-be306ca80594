<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <span>基本信息</span>
      </div>
      <div class="info-table">
        <el-row :gutter="0">
          <el-col :span="4" class="info-label">游客ID</el-col>
          <el-col :span="8" class="info-value">{{ visitorInfo.visitorId }}</el-col>
          <el-col :span="4" class="info-label">游客账号</el-col>
          <el-col :span="8" class="info-value">{{ visitorInfo.touristAccount }}</el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="4" class="info-label">注册时间</el-col>
          <el-col :span="8" class="info-value">{{ visitorInfo.createTime }}</el-col>
          <el-col :span="4" class="info-label"></el-col>
          <el-col :span="8" class="info-value"></el-col>
        </el-row>
      </div>
    </el-card>

    <el-card class="box-card">
      <div slot="header" class="card-header">
        <span>账户明细</span>
      </div>
      <div class="account-summary">
        <span class="summary-item">游豆余额: <span class="amount-red">10000</span></span>
        <span class="summary-item">累计奖励游豆: <span class="amount-red">1500</span></span>
      </div>
      <el-table :data="transactionList" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="transactionId" label="交易单号" align="center"></el-table-column>
        <el-table-column prop="time" label="交易时间" align="center" width="180"></el-table-column>
        <el-table-column prop="type" label="交易类型" align="center"></el-table-column>
        <el-table-column prop="payment" label="支付现金（元）" align="center"></el-table-column>
        <el-table-column prop="balance" label="游豆余额" align="center"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <div style="text-align: center; margin-top: 20px;">
      <el-button @click="handleBack">返回</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "VisitorView",
  data() {
    return {
      visitorInfo: {},
      transactionList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    const visitorId = this.$route.params && this.$route.params.visitorId;
    this.getVisitorDetails(visitorId);
    this.getList();
  },
  methods: {
    getVisitorDetails(visitorId) {
      // Mock data from screenshot
      this.visitorInfo = {
        visitorId: "****************",
        touristAccount: "***********",
        createTime: "2017-07-24 17:25:38",
      };
    },
    getList() {
      const allTransactions = [
        { transactionId: '', time: '2017-01-19 14:48:38', type: '劳务奖励', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 23:59:59', type: '消费', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 14:48:38', type: '推荐充值', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 23:59:59', type: '消费', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 14:48:38', type: '推荐用户', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 23:59:59', type: '消费', payment: '--', balance: '' },
        { transactionId: '', time: '2017-01-19 14:48:38', type: '充值', payment: '99', balance: '9798' },
        { transactionId: '', time: '2017-01-19 23:59:59', type: '消费', payment: '--', balance: '500' },
        { transactionId: '', time: '2017-01-19 14:48:38', type: '充值', payment: '99', balance: '10149' },
        { transactionId: '', time: '2017-01-19 14:48:38', type: '新注册', payment: '--', balance: '10000' },
        // Mock data for pagination
        { transactionId: 'mock11', time: '2017-01-18 10:00:00', type: '消费', payment: '--', balance: '10000' },
        { transactionId: 'mock12', time: '2017-01-18 11:00:00', type: '充值', payment: '100', balance: '10100' },
        { transactionId: 'mock13', time: '2017-01-18 12:00:00', type: '消费', payment: '--', balance: '10000' },
        { transactionId: 'mock14', time: '2017-01-18 13:00:00', type: '消费', payment: '--', balance: '9900' },
        { transactionId: 'mock15', time: '2017-01-18 14:00:00', type: '充值', payment: '50', balance: '9950' },
        { transactionId: 'mock16', time: '2017-01-18 15:00:00', type: '消费', payment: '--', balance: '9900' },
        { transactionId: 'mock17', time: '2017-01-18 16:00:00', type: '消费', payment: '--', balance: '9800' },
        { transactionId: 'mock18', time: '2017-01-18 17:00:00', type: '消费', payment: '--', balance: '9700' },
        { transactionId: 'mock19', time: '2017-01-18 18:00:00', type: '充值', payment: '200', balance: '9900' },
        { transactionId: 'mock20', time: '2017-01-18 19:00:00', type: '消费', payment: '--', balance: '9800' },
        { transactionId: 'mock21', time: '2017-01-18 20:00:00', type: '消费', payment: '--', balance: '9700' },
      ];
      this.total = allTransactions.length;
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;
      this.transactionList = allTransactions.slice(start, end).reverse();
    },
    handleBack() {
      this.$router.push('/touristCenter/visitorManagement');
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
  &:before {
    content: '';
    border-left: 4px solid #409EFF;
    margin-right: 8px;
  }
}

.info-table {
  border: 1px solid #EBEEF5;
  .el-row {
    border-bottom: 1px solid #EBEEF5;
    &:last-child {
      border-bottom: none;
    }
  }
  .info-label {
    background-color: #F5F7FA;
    padding: 12px;
    text-align: center;
    font-weight: 500;
    color: #909399;
    border-right: 1px solid #EBEEF5;
  }
  .info-value {
    padding: 12px;
  }
  .el-col {
    min-height: 49px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
   .info-value {
    justify-content: flex-start;
  }
}

.account-summary {
  background-color: #fff9f9;
  border: 1px solid #fde2e2;
  padding: 20px;
  border-radius: 4px;
}

.summary-item {
  margin-right: 40px;
  font-size: 16px;
  font-weight: bold;
}

.amount-red {
  color: #f56c6c;
}
</style> 